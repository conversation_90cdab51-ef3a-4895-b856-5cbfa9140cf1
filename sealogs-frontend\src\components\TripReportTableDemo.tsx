import { cn } from '@/app/lib/utils'
import React from 'react'
import dayjs from 'dayjs'

// Mock crew members data
const crewMembers = [
    { id: 1, firstName: 'Captain', surname: '<PERSON>' },
    { id: 2, firstName: 'First', surname: 'Officer' },
    { id: 3, firstName: 'Engineer', surname: '<PERSON>' },
]

// Mock dangerous goods types
const goodsTypes = [
    { label: 'Class 1 Explosives', value: '1' },
    { label: 'Class 2 Gases', value: '2' },
    { label: 'Class 2.1 - Flammable gases', value: '2.1' },
    { label: 'Class 2.2 - Non-Flammable Non-Toxic Gases', value: '2.2' },
    { label: 'Class 2.3 - Toxic Gases', value: '2.3' },
    { label: 'Class 3 Flammable Liquids', value: '3' },
    { label: 'Class 4 Flammable Solids', value: '4' },
    { label: 'Class 4.1 - Flammable Solids', value: '4.1' },
    { label: 'Class 4.2 - Spontaneously Combustible Substances', value: '4.2' },
    { label: 'Class 4.3 - Substances Flammable When Wet', value: '4.3' },
    { label: 'Class 5 Oxidizing Substances and Organic Peroxides', value: '5' },
    { label: 'Class 5.1 - Oxidizing Substances', value: '5.1' },
    { label: 'Class 5.2 - Organic Peroxides', value: '5.2' },
    { label: 'Class 6 Toxic and Infectious Substances', value: '6' },
    { label: 'Class 6.1 - Toxic Substances', value: '6.1' },
    { label: 'Class 6.2 - Infectious Substances', value: '6.2' },
    { label: 'Class 7 Radioactive Material', value: '7' },
    { label: 'Class 8 Corrosive Substances', value: '8' },
    { label: 'Class 9 Miscellaneous Dangerous Substances', value: '9' },
]

// Mock functions
const getTripPOB = (trip: any) => {
    let totalGuests = 0
    const supernumeraries = trip.tripEvents?.nodes?.filter((event: any) => {
        return event.eventCategory === 'Supernumerary'
    })
    if (supernumeraries?.length > 0) {
        supernumeraries.forEach((s: any) => {
            totalGuests += s.eventType_Supernumerary?.pob || 0
        })
    }
    const paxJoined =
        trip.tripReport_Stops?.nodes?.reduce((acc: number, stop: any) => {
            return acc + (stop.paxJoined || 0) - (stop.paxDeparted || 0)
        }, 0) || 0
    return (trip?.pob || 0) + crewMembers?.length + totalGuests + paxJoined
}

const displayReportField = (fieldName: string) => {
    // Mock implementation - return true for common fields
    const commonFields = [
        'DesignatedDangerousGoodsSailing',
        'DangerousGoods',
        'PassengerVehiclePickDrop',
    ]
    return commonFields.includes(fieldName)
}

const displayEventTypeField = (fieldName: string) => {
    // Mock implementation - return true for common event type fields
    const commonEventFields = [
        'PassengerVehiclePickDrop',
        'BarCrossing',
        'RestrictedVisibility',
    ]
    return commonEventFields.includes(fieldName)
}

// Mock helper functions
const formatDate = (date: any) => {
    return dayjs(date).format('DD/MM/YYYY')
}

const getActivityType = (eventType: any, trip: any) => {
    return eventType?.type || 'General Activity'
}

const formatDateTime = (dateTime: any) => {
    return dayjs(dateTime).format('DD/MM/YYYY HH:mm')
}

const getTowingChecklist = (eventType: any, trip: any) => {
    // Mock towing checklist data
    return {
        towLineSecured: true,
        communicationEstablished: true,
        safetyBriefingCompleted: false,
        weatherConditionsChecked: true,
        riskFactors: {
            nodes: [
                {
                    id: 1,
                    title: 'Weather conditions',
                    impact: 'Medium',
                    probability: 6,
                },
                {
                    id: 2,
                    title: 'Towing line tension',
                    impact: 'High',
                    probability: 4,
                },
            ],
        },
        member: {
            id: 1,
            firstName: 'Rescue',
            surname: 'Officer',
        },
    }
}

const getPersonRescueChecklist = (eventType: any, trip: any) => {
    // Mock person rescue checklist data
    return {
        rescueEquipmentReady: true,
        medicalKitAvailable: true,
        communicationWithCoastGuard: true,
        crewBriefingCompleted: false,
        riskFactors: {
            nodes: [
                {
                    id: 1,
                    title: 'Sea conditions',
                    impact: 'High',
                    probability: 7,
                },
            ],
        },
        member: {
            id: 2,
            firstName: 'Medical',
            surname: 'Officer',
        },
    }
}

const getVesselRescueChecklist = (eventType: any, trip: any) => {
    // Mock vessel rescue checklist data
    return {
        towingEquipmentReady: true,
        safetyLinesSecured: true,
        emergencyContactsMade: true,
        weatherAssessmentComplete: false,
        riskFactors: {
            nodes: [
                {
                    id: 1,
                    title: 'Vessel stability',
                    impact: 'High',
                    probability: 5,
                },
            ],
        },
        member: {
            id: 3,
            firstName: 'Chief',
            surname: 'Engineer',
        },
    }
}

const getInfringementsBody = (infringementId: any) => {
    // Mock infringement data
    return (
        <tbody>
            <tr>
                <td>Infringement Notice #{infringementId}</td>
                <td>Speed limit violation</td>
                <td>$150.00</td>
                <td>Pending</td>
            </tr>
        </tbody>
    )
}

const getLogBookConfig = () => {
    // Mock logbook configuration
    return {
        customisedLogBookComponents: {
            nodes: [
                { componentClass: 'TripReport_LogBookEntrySection' },
                { componentClass: 'CrewTraining_LogBookEntrySection' },
                { componentClass: 'MaintenanceCheck_LogBookEntrySection' },
            ],
        },
    }
}

// Mock trip report data
export const tripReportDemo = [
    {
        id: 1,
        departTime: '08:00',
        arriveTime: '17:30',
        arrive: '2024-01-15T17:30:00Z',
        pob: 25,
        totalVehiclesCarried: 8,
        designatedDangerousGoodsSailing: true,
        fromLocation: {
            id: 1,
            title: 'Auckland Ferry Terminal',
            lat: -36.8485,
            long: 174.7633,
        },
        toLocation: {
            id: 2,
            title: 'Waiheke Island',
            lat: -36.8,
            long: 175.0833,
        },
        dangerousGoodsRecords: {
            nodes: [
                {
                    id: 1,
                    type: '3',
                    comment:
                        '<p>Flammable liquids - properly secured and documented</p>',
                },
                {
                    id: 2,
                    type: '2.1',
                    comment: '<p>Compressed gas cylinders - safety checked</p>',
                },
            ],
        },
        dangerousGoodsChecklist: {
            id: 1,
            properSecurement: true,
            appropriateSignage: true,
            emergencyEquipment: true,
            ventilationAdequate: false,
            separationMaintained: true,
            riskFactors: {
                nodes: [
                    {
                        id: 1,
                        title: 'Weather conditions',
                        impact: 'Medium',
                        probability: 7,
                    },
                    {
                        id: 2,
                        title: 'Loading procedures',
                        impact: 'Low',
                        probability: 3,
                    },
                ],
            },
            member: {
                id: 1,
                firstName: 'Safety',
                surname: 'Officer',
            },
        },
        tripReport_Stops: {
            nodes: [
                {
                    id: 1,
                    stopLocation: {
                        id: 3,
                        title: 'Matiatia Wharf',
                    },
                    arriveTime: '09:15',
                    departTime: '09:45',
                    paxJoined: 12,
                    paxDeparted: 5,
                    vehiclesJoined: 3,
                    vehiclesDeparted: 1,
                    designatedDangerousGoodsSailing: false,
                    otherCargo: 'Tourist equipment and supplies',
                    comments:
                        'Smooth boarding process, weather conditions good',
                },
                {
                    id: 2,
                    stopLocation: {
                        id: 4,
                        title: 'Oneroa Beach',
                    },
                    arriveTime: '14:30',
                    departTime: '15:00',
                    paxJoined: 8,
                    paxDeparted: 15,
                    vehiclesJoined: 2,
                    vehiclesDeparted: 4,
                    designatedDangerousGoodsSailing: false,
                    comments:
                        'Busy afternoon departure, all safety protocols followed',
                },
            ],
        },
        tripEvents: {
            nodes: [
                {
                    id: 1,
                    eventCategory: 'BarCrossing',
                    eventType_BarCrossing: {
                        id: 1,
                        geoLocation: {
                            id: 1,
                            title: 'Manukau Bar',
                        },
                        time: '08:30',
                        timeCompleted: '09:15',
                        barCrossingChecklist: {
                            id: 1,
                            weatherConditions: true,
                            tideConditions: true,
                            vesselReadiness: true,
                            crewBriefing: false,
                            member: {
                                id: 1,
                                firstName: 'Captain',
                                surname: 'Smith',
                            },
                        },
                    },
                },
                {
                    id: 2,
                    eventCategory: 'RestrictedVisibility',
                    eventType_RestrictedVisibility: {
                        id: 2,
                        geoLocation: {
                            id: 2,
                            title: 'Auckland Harbour',
                        },
                        time: '10:00',
                        timeCompleted: '11:30',
                        visibility: 'Poor',
                        speedReduction: true,
                        radarUse: true,
                        member: {
                            id: 1,
                            firstName: 'Captain',
                            surname: 'Smith',
                        },
                    },
                },
                {
                    id: 3,
                    eventCategory: 'CrewTraining',
                    crewTraining: {
                        id: 3,
                        date: '2024-01-15',
                        time: '14:00',
                        timeCompleted: '16:00',
                        trainer: {
                            id: 1,
                            firstName: 'John',
                            surname: 'Smith',
                        },
                        geoLocation: {
                            id: 1,
                            title: 'Training Bay',
                        },
                        members: {
                            nodes: [
                                {
                                    id: 1,
                                    firstName: 'Alice',
                                    surname: 'Johnson',
                                },
                                { id: 2, firstName: 'Bob', surname: 'Wilson' },
                            ],
                        },
                        trainingTypes: {
                            nodes: [
                                { id: 1, title: 'Safety Procedures' },
                                { id: 2, title: 'Navigation' },
                            ],
                        },
                        trainingSummary:
                            '<p>Comprehensive safety and navigation training completed successfully.</p>',
                    },
                },
                {
                    id: 4,
                    eventCategory: 'Tasking',
                    eventType_Tasking: {
                        id: 4,
                        type: 'SearchAndRescue',
                        time: '12:00',
                        title: 'Missing vessel assistance',
                        geoLocation: { id: 1, title: 'Hauraki Gulf' },
                    },
                },
                {
                    id: 5,
                    eventCategory: 'Supernumerary',
                    eventType_Supernumerary: {
                        id: 5,
                        pob: 15,
                        geoLocation: { id: 1, title: 'Auckland Harbour' },
                        time: '09:00',
                    },
                },
            ],
        },
        comment:
            '<p>Successful trip with good weather conditions. All safety protocols followed.</p>',
    },
]

export default function TripReportTableDemo() {
    const tableClass =
        'w-full table-fixed [&_td]:text-start [&_th]:text-outer-space-600 [&_th]:text-start [&_td]:px-6 [&_td]:py-3 [&_th]:px-6 [&_th]:py-3 [&_tr]:border-outer-space-200 [&_tr:not(:first-child)]:border-t [&_thead]:border-b [&_thead]:border-outer-space-400'

    return (
        <div>
            {tripReportDemo && (
                <div className="mt-8 mb-4">
                    <h5 className=" ">Trip report</h5>
                </div>
            )}
            {tripReportDemo &&
                tripReportDemo.map((trip: any) => (
                    <div
                        key={trip.id}
                        className="border border-outer-space-400 rounded-lg overflow-hidden mb-4">
                        <table className={tableClass}>
                            <tbody>
                                <tr>
                                    <th>Departure:</th>
                                    <td>{trip?.departTime}</td>
                                    <th>Depart location</th>
                                    <td>
                                        {trip?.fromLocation?.id > 0
                                            ? trip?.fromLocation?.title
                                            : trip?.fromLocation?.lat +
                                              ' ' +
                                              trip?.fromLocation?.long}
                                    </td>
                                </tr>
                                <tr>
                                    <th>P.O.B</th>
                                    <td>{getTripPOB(trip)}</td>
                                    <th>Vehicles on board</th>
                                    <td>{trip?.totalVehiclesCarried}</td>
                                </tr>
                                {trip?.designatedDangerousGoodsSailing &&
                                    displayReportField(
                                        'DesignatedDangerousGoodsSailing',
                                    ) && (
                                        <tr>
                                            <th colSpan={4}>
                                                This is a designated dangerous
                                                goods sailing
                                            </th>
                                        </tr>
                                    )}
                                {trip?.dangerousGoodsRecords?.nodes?.length >
                                    0 && (
                                    <tr>
                                        <th>Dangerous goods</th>
                                        <td colSpan={3}>
                                            {trip?.dangerousGoodsRecords?.nodes?.map(
                                                (item: any, index: number) => (
                                                    <React.Fragment
                                                        key={item.id}>
                                                        {index > 0 && ', '}
                                                        {
                                                            goodsTypes.find(
                                                                (type) =>
                                                                    type.value ===
                                                                    item?.type,
                                                            )?.label
                                                        }
                                                        {' - '}
                                                        <div
                                                            className="inline-block"
                                                            dangerouslySetInnerHTML={{
                                                                __html: item?.comment,
                                                            }}></div>
                                                    </React.Fragment>
                                                ),
                                            )}
                                        </td>
                                    </tr>
                                )}
                                {displayEventTypeField(
                                    'PassengerVehiclePickDrop',
                                ) &&
                                    trip?.dangerousGoodsChecklist?.id > 0 && (
                                        <tr
                                            key={
                                                trip.dangerousGoodsChecklist.id
                                            }>
                                            <td colSpan={4}>
                                                <table
                                                    className={cn(
                                                        tableClass,
                                                        '[&_td:first-child]:!px-0',
                                                    )}>
                                                    <tbody
                                                        className={cn(
                                                            tableClass,
                                                            '[&_td:first-child]:!px-0',
                                                        )}>
                                                        <tr>
                                                            <td>
                                                                Dangerous goods
                                                                checklist
                                                            </td>
                                                            <td>
                                                                <table
                                                                    className={cn(
                                                                        tableClass,
                                                                        '[&_td:first-child]:!px-0',
                                                                    )}>
                                                                    <tbody>
                                                                        {Object.entries(
                                                                            trip.dangerousGoodsChecklist,
                                                                        ).filter(
                                                                            ([
                                                                                key,
                                                                                value,
                                                                            ]) =>
                                                                                value ===
                                                                                true,
                                                                        )
                                                                            .length >
                                                                            0 && (
                                                                            <tr>
                                                                                <td>
                                                                                    {Object.entries(
                                                                                        trip.dangerousGoodsChecklist,
                                                                                    )
                                                                                        .filter(
                                                                                            ([
                                                                                                key,
                                                                                                value,
                                                                                            ]) =>
                                                                                                value ===
                                                                                                true,
                                                                                        )
                                                                                        .map(
                                                                                            ([
                                                                                                key,
                                                                                                value,
                                                                                            ]) => ({
                                                                                                key,
                                                                                                value,
                                                                                            }),
                                                                                        )
                                                                                        .map(
                                                                                            (
                                                                                                item,
                                                                                            ) => {
                                                                                                return item.key
                                                                                                    .replace(
                                                                                                        /([A-Z])/g,
                                                                                                        ' $1',
                                                                                                    )
                                                                                                    .replace(
                                                                                                        /^./,
                                                                                                        (
                                                                                                            str,
                                                                                                        ) =>
                                                                                                            str.toUpperCase(),
                                                                                                    )
                                                                                            },
                                                                                        )
                                                                                        .join(
                                                                                            ', ',
                                                                                        )}
                                                                                </td>
                                                                                <td>
                                                                                    Ok
                                                                                </td>
                                                                            </tr>
                                                                        )}
                                                                        {Object.entries(
                                                                            trip.dangerousGoodsChecklist,
                                                                        ).filter(
                                                                            ([
                                                                                key,
                                                                                value,
                                                                            ]) =>
                                                                                value ===
                                                                                false,
                                                                        )
                                                                            .length >
                                                                            0 && (
                                                                            <tr>
                                                                                <td>
                                                                                    {Object.entries(
                                                                                        trip.dangerousGoodsChecklist,
                                                                                    )
                                                                                        .filter(
                                                                                            ([
                                                                                                key,
                                                                                                value,
                                                                                            ]) =>
                                                                                                value ===
                                                                                                false,
                                                                                        )
                                                                                        .map(
                                                                                            ([
                                                                                                key,
                                                                                                value,
                                                                                            ]) => ({
                                                                                                key,
                                                                                                value,
                                                                                            }),
                                                                                        )
                                                                                        .map(
                                                                                            (
                                                                                                item,
                                                                                            ) => {
                                                                                                return item.key
                                                                                                    .replace(
                                                                                                        /([A-Z])/g,
                                                                                                        ' $1',
                                                                                                    )
                                                                                                    .replace(
                                                                                                        /^./,
                                                                                                        (
                                                                                                            str,
                                                                                                        ) =>
                                                                                                            str.toUpperCase(),
                                                                                                    )
                                                                                            },
                                                                                        )
                                                                                        .join(
                                                                                            ', ',
                                                                                        )}
                                                                                </td>
                                                                                <td>
                                                                                    Not
                                                                                    Ok
                                                                                </td>
                                                                            </tr>
                                                                        )}
                                                                        {trip
                                                                            ?.dangerousGoodsChecklist
                                                                            ?.riskFactors
                                                                            ?.nodes
                                                                            ?.length >
                                                                            0 && (
                                                                            <tr>
                                                                                <td
                                                                                    colSpan={
                                                                                        2
                                                                                    }>
                                                                                    {trip?.dangerousGoodsChecklist.riskFactors.nodes.map(
                                                                                        (
                                                                                            item: any,
                                                                                            index: number,
                                                                                        ) => (
                                                                                            <div
                                                                                                className="inline-block"
                                                                                                key={
                                                                                                    item.id
                                                                                                }>
                                                                                                {index >
                                                                                                0
                                                                                                    ? ', '
                                                                                                    : 'Risk factors: '}
                                                                                                {`${item.title} - ${item.impact} - ${item.probability}/10`}
                                                                                            </div>
                                                                                        ),
                                                                                    )}
                                                                                </td>
                                                                            </tr>
                                                                        )}
                                                                        {trip
                                                                            ?.dangerousGoodsChecklist
                                                                            ?.member
                                                                            ?.id >
                                                                            0 && (
                                                                            <tr>
                                                                                <td
                                                                                    colSpan={
                                                                                        2
                                                                                    }>
                                                                                    {`Author: ${trip.dangerousGoodsChecklist.member?.firstName} ${trip.dangerousGoodsChecklist.member?.surname}`}
                                                                                </td>
                                                                            </tr>
                                                                        )}
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    )}
                                {trip?.tripReport_Stops?.nodes?.length > 0 && (
                                    <>
                                        {trip?.tripReport_Stops?.nodes?.map(
                                            (stop: any) => (
                                                <tr key={stop.id}>
                                                    <td colSpan={3}>
                                                        <table
                                                            className={cn(
                                                                tableClass,
                                                                '[&_td:first-child]:!px-0',
                                                            )}>
                                                            <tbody>
                                                                <tr>
                                                                    <th>
                                                                        Trip
                                                                        stop
                                                                    </th>
                                                                    <td
                                                                        colSpan={
                                                                            3
                                                                        }>
                                                                        <table
                                                                            className={cn(
                                                                                tableClass,
                                                                                '[&_td:first-child]:!px-0',
                                                                            )}>
                                                                            <tbody>
                                                                                <tr
                                                                                    key={`${stop.id}-stop-location`}>
                                                                                    {' '}
                                                                                    <th>
                                                                                        Location:
                                                                                    </th>
                                                                                    <td>
                                                                                        {stop
                                                                                            ?.stopLocation
                                                                                            ?.id >
                                                                                        0
                                                                                            ? stop
                                                                                                  ?.stopLocation
                                                                                                  ?.title
                                                                                            : ''}
                                                                                    </td>
                                                                                    <td>
                                                                                        {stop?.designatedDangerousGoodsSailing &&
                                                                                        displayReportField(
                                                                                            'DesignatedDangerousGoodsSailing',
                                                                                        )
                                                                                            ? 'This is a designated dangerous goods sailing'
                                                                                            : '-'}
                                                                                    </td>
                                                                                </tr>
                                                                                <tr
                                                                                    key={`${stop.id}-arr-time`}>
                                                                                    <th>
                                                                                        Arrival:
                                                                                    </th>
                                                                                    <td>
                                                                                        {
                                                                                            stop?.arriveTime
                                                                                        }
                                                                                    </td>
                                                                                    <th>
                                                                                        Departure:
                                                                                    </th>
                                                                                    <td>
                                                                                        {
                                                                                            stop?.departTime
                                                                                        }
                                                                                    </td>
                                                                                </tr>
                                                                                <tr
                                                                                    key={`${stop.id}-pax`}>
                                                                                    <th>
                                                                                        Pax
                                                                                        joined:
                                                                                    </th>
                                                                                    <td>
                                                                                        {
                                                                                            stop?.paxJoined
                                                                                        }
                                                                                    </td>
                                                                                    <th>
                                                                                        Pax
                                                                                        departed:
                                                                                    </th>
                                                                                    <td>
                                                                                        {
                                                                                            stop?.paxDeparted
                                                                                        }
                                                                                    </td>
                                                                                </tr>
                                                                                <tr
                                                                                    key={`${stop.id}-vehicles`}>
                                                                                    <th>
                                                                                        Vehicles
                                                                                        joined:
                                                                                    </th>
                                                                                    <td>
                                                                                        {
                                                                                            stop?.vehiclesJoined
                                                                                        }
                                                                                    </td>{' '}
                                                                                    <th>
                                                                                        Vehicles
                                                                                        departed:
                                                                                    </th>
                                                                                    <td>
                                                                                        {
                                                                                            stop?.vehiclesDeparted
                                                                                        }
                                                                                    </td>
                                                                                </tr>
                                                                                {stop?.otherCargo && (
                                                                                    <tr
                                                                                        key={`${stop.id}-otherCargo`}>
                                                                                        <th>
                                                                                            Other
                                                                                            cargo:
                                                                                        </th>
                                                                                        <td
                                                                                            colSpan={
                                                                                                2
                                                                                            }>
                                                                                            {
                                                                                                stop?.otherCargo
                                                                                            }
                                                                                        </td>
                                                                                    </tr>
                                                                                )}
                                                                                {stop?.comments && (
                                                                                    <tr
                                                                                        key={`${stop.id}-comments`}>
                                                                                        <th>
                                                                                            Comments:
                                                                                        </th>
                                                                                        <td
                                                                                            colSpan={
                                                                                                2
                                                                                            }>
                                                                                            {
                                                                                                stop?.comments
                                                                                            }
                                                                                        </td>
                                                                                    </tr>
                                                                                )}
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                            ),
                                        )}
                                    </>
                                )}
                                {trip?.tripEvents?.nodes?.length > 0 && (
                                    <>
                                        <tr>
                                            <td colSpan={4}>
                                                <table
                                                    className={cn(
                                                        tableClass,
                                                        '[&_td:first-child]:!px-0',
                                                    )}>
                                                    <tbody>
                                                        {trip?.tripEvents?.nodes?.map(
                                                            (event: any) => (
                                                                <>
                                                                    {event.eventCategory ===
                                                                        'BarCrossing' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Bar
                                                                                    crossing
                                                                                </th>
                                                                                <td
                                                                                    colSpan={
                                                                                        4
                                                                                    }>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.eventType_BarCrossing.id}-bc-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Event
                                                                                                        type:
                                                                                                    </strong>{' '}
                                                                                                    Bar
                                                                                                    crossing
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_BarCrossing
                                                                                                        ?.geoLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.eventType_BarCrossing
                                                                                                              ?.geoLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-crossing-time`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Crossing
                                                                                                        start:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_BarCrossing
                                                                                                        ?.time ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Crossing
                                                                                                        end:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_BarCrossing
                                                                                                        ?.timeCompleted ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            {event
                                                                                                ?.eventType_BarCrossing
                                                                                                ?.barCrossingChecklist
                                                                                                ?.id >
                                                                                                0 && (
                                                                                                <tr
                                                                                                    key={`${event.id}-bc-checklist`}>
                                                                                                    <td
                                                                                                        colSpan={
                                                                                                            2
                                                                                                        }>
                                                                                                        <table
                                                                                                            className={cn(
                                                                                                                tableClass,
                                                                                                                '[&_td:first-child]:!px-0',
                                                                                                            )}>
                                                                                                            <tbody>
                                                                                                                <tr>
                                                                                                                    <th>
                                                                                                                        Bar
                                                                                                                        crossing
                                                                                                                        checklist
                                                                                                                    </th>
                                                                                                                    <td className="p-0">
                                                                                                                        <table
                                                                                                                            className={cn(
                                                                                                                                tableClass,
                                                                                                                                '[&_td:first-child]:!px-0',
                                                                                                                            )}>
                                                                                                                            <tbody>
                                                                                                                                {Object.entries(
                                                                                                                                    event
                                                                                                                                        ?.eventType_BarCrossing
                                                                                                                                        ?.barCrossingChecklist,
                                                                                                                                ).filter(
                                                                                                                                    ([
                                                                                                                                        key,
                                                                                                                                        value,
                                                                                                                                    ]) =>
                                                                                                                                        value ===
                                                                                                                                        true,
                                                                                                                                )
                                                                                                                                    .length >
                                                                                                                                    0 && (
                                                                                                                                    <tr
                                                                                                                                        key={`${event.id}-bc-checklist-y`}>
                                                                                                                                        <td>
                                                                                                                                            {Object.entries(
                                                                                                                                                event
                                                                                                                                                    ?.eventType_BarCrossing
                                                                                                                                                    ?.barCrossingChecklist,
                                                                                                                                            )
                                                                                                                                                .filter(
                                                                                                                                                    ([
                                                                                                                                                        key,
                                                                                                                                                        value,
                                                                                                                                                    ]) =>
                                                                                                                                                        value ===
                                                                                                                                                        true,
                                                                                                                                                )
                                                                                                                                                .map(
                                                                                                                                                    ([
                                                                                                                                                        key,
                                                                                                                                                        value,
                                                                                                                                                    ]) => {
                                                                                                                                                        return key
                                                                                                                                                            .replace(
                                                                                                                                                                /([A-Z])/g,
                                                                                                                                                                ' $1',
                                                                                                                                                            )
                                                                                                                                                            .replace(
                                                                                                                                                                /^./,
                                                                                                                                                                (
                                                                                                                                                                    str,
                                                                                                                                                                ) =>
                                                                                                                                                                    str.toUpperCase(),
                                                                                                                                                            )
                                                                                                                                                    },
                                                                                                                                                )
                                                                                                                                                .join(
                                                                                                                                                    ', ',
                                                                                                                                                )}
                                                                                                                                        </td>
                                                                                                                                        <td>
                                                                                                                                            Ok
                                                                                                                                        </td>
                                                                                                                                    </tr>
                                                                                                                                )}
                                                                                                                                {Object.entries(
                                                                                                                                    event
                                                                                                                                        ?.eventType_BarCrossing
                                                                                                                                        ?.barCrossingChecklist,
                                                                                                                                ).filter(
                                                                                                                                    ([
                                                                                                                                        key,
                                                                                                                                        value,
                                                                                                                                    ]) =>
                                                                                                                                        value ===
                                                                                                                                        false,
                                                                                                                                )
                                                                                                                                    .length >
                                                                                                                                    0 && (
                                                                                                                                    <tr
                                                                                                                                        key={`${event.id}-bc-checklist-n`}>
                                                                                                                                        <td>
                                                                                                                                            {Object.entries(
                                                                                                                                                event
                                                                                                                                                    ?.eventType_BarCrossing
                                                                                                                                                    ?.barCrossingChecklist,
                                                                                                                                            )
                                                                                                                                                .filter(
                                                                                                                                                    ([
                                                                                                                                                        key,
                                                                                                                                                        value,
                                                                                                                                                    ]) =>
                                                                                                                                                        value ===
                                                                                                                                                        false,
                                                                                                                                                )
                                                                                                                                                .map(
                                                                                                                                                    ([
                                                                                                                                                        key,
                                                                                                                                                        value,
                                                                                                                                                    ]) => {
                                                                                                                                                        return key
                                                                                                                                                            .replace(
                                                                                                                                                                /([A-Z])/g,
                                                                                                                                                                ' $1',
                                                                                                                                                            )
                                                                                                                                                            .replace(
                                                                                                                                                                /^./,
                                                                                                                                                                (
                                                                                                                                                                    str,
                                                                                                                                                                ) =>
                                                                                                                                                                    str.toUpperCase(),
                                                                                                                                                            )
                                                                                                                                                    },
                                                                                                                                                )
                                                                                                                                                .join(
                                                                                                                                                    ', ',
                                                                                                                                                )}
                                                                                                                                        </td>
                                                                                                                                        <td>
                                                                                                                                            Not
                                                                                                                                            Ok
                                                                                                                                        </td>
                                                                                                                                    </tr>
                                                                                                                                )}
                                                                                                                            </tbody>
                                                                                                                        </table>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            </tbody>
                                                                                                        </table>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            )}
                                                                                            {trip
                                                                                                ?.eventType_BarCrossing
                                                                                                ?.barCrossingChecklist
                                                                                                ?.riskFactors
                                                                                                ?.nodes
                                                                                                ?.length >
                                                                                                0 && (
                                                                                                <tr>
                                                                                                    <td
                                                                                                        colSpan={
                                                                                                            2
                                                                                                        }>
                                                                                                        {trip?.eventType_BarCrossing?.barCrossingChecklist.riskFactors.nodes.map(
                                                                                                            (
                                                                                                                item: any,
                                                                                                                index: number,
                                                                                                            ) => (
                                                                                                                <div
                                                                                                                    className="inline-block"
                                                                                                                    key={
                                                                                                                        item.id
                                                                                                                    }>
                                                                                                                    {index >
                                                                                                                    0
                                                                                                                        ? ', '
                                                                                                                        : 'Risk factors: '}
                                                                                                                    {`${item.title} - ${item.impact} - ${item.probability}/10`}
                                                                                                                </div>
                                                                                                            ),
                                                                                                        )}
                                                                                                    </td>
                                                                                                </tr>
                                                                                            )}
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'RestrictedVisibility' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Restricted
                                                                                    visibility
                                                                                </th>
                                                                                <td
                                                                                    colSpan={
                                                                                        4
                                                                                    }>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.eventType_RestrictedVisibility.id}-rv-type`}>
                                                                                                <td
                                                                                                    colSpan={
                                                                                                        2
                                                                                                    }>
                                                                                                    <strong>
                                                                                                        Event
                                                                                                        Type:
                                                                                                    </strong>{' '}
                                                                                                    Restricted
                                                                                                    visibility
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.eventType_RestrictedVisibility.id}-locationrv`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Start
                                                                                                        location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RestrictedVisibility
                                                                                                        ?.startLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.eventType_RestrictedVisibility
                                                                                                              ?.startLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        End
                                                                                                        location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RestrictedVisibility
                                                                                                        ?.endLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.eventType_RestrictedVisibility
                                                                                                              ?.endLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-locationbc`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Crossing
                                                                                                        Time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RestrictedVisibility
                                                                                                        ?.crossingTime ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Crossed
                                                                                                        time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RestrictedVisibility
                                                                                                        ?.crossedTime ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-speed`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Safe
                                                                                                        speed:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RestrictedVisibility
                                                                                                        ?.estSafeSpeed ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Avg
                                                                                                        speed:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RestrictedVisibility
                                                                                                        ?.approxSafeSpeed ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-rv-checklist`}>
                                                                                                <td
                                                                                                    colSpan={
                                                                                                        2
                                                                                                    }>
                                                                                                    <table
                                                                                                        className={cn(
                                                                                                            tableClass,
                                                                                                            '[&_td:first-child]:!px-0',
                                                                                                        )}>
                                                                                                        <tbody>
                                                                                                            <tr>
                                                                                                                <th>
                                                                                                                    Safe
                                                                                                                    operating
                                                                                                                    procedures
                                                                                                                    checklist:
                                                                                                                </th>
                                                                                                                <td className="p-0">
                                                                                                                    <table
                                                                                                                        className={
                                                                                                                            tableClass
                                                                                                                        }>
                                                                                                                        <tbody>
                                                                                                                            {Object.entries(
                                                                                                                                event?.eventType_RestrictedVisibility,
                                                                                                                            ).filter(
                                                                                                                                ([
                                                                                                                                    key,
                                                                                                                                    value,
                                                                                                                                ]) =>
                                                                                                                                    value ===
                                                                                                                                    true,
                                                                                                                            )
                                                                                                                                .length >
                                                                                                                                0 && (
                                                                                                                                <tr
                                                                                                                                    key={`${event.id}-rv-checklist-y`}>
                                                                                                                                    <td>
                                                                                                                                        {Object.entries(
                                                                                                                                            event?.eventType_RestrictedVisibility,
                                                                                                                                        )
                                                                                                                                            .filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    true,
                                                                                                                                            )
                                                                                                                                            .map(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) => {
                                                                                                                                                    return key
                                                                                                                                                        .replace(
                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                            ' $1',
                                                                                                                                                        )
                                                                                                                                                        .replace(
                                                                                                                                                            /^./,
                                                                                                                                                            (
                                                                                                                                                                str,
                                                                                                                                                            ) =>
                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                        )
                                                                                                                                                },
                                                                                                                                            )
                                                                                                                                            .join(
                                                                                                                                                ', ',
                                                                                                                                            )}
                                                                                                                                    </td>
                                                                                                                                    <td>
                                                                                                                                        Ok
                                                                                                                                    </td>
                                                                                                                                </tr>
                                                                                                                            )}
                                                                                                                            {Object.entries(
                                                                                                                                event?.eventType_RestrictedVisibility,
                                                                                                                            ).filter(
                                                                                                                                ([
                                                                                                                                    key,
                                                                                                                                    value,
                                                                                                                                ]) =>
                                                                                                                                    value ===
                                                                                                                                    false,
                                                                                                                            )
                                                                                                                                .length >
                                                                                                                                0 && (
                                                                                                                                <tr
                                                                                                                                    key={`${event.id}-rv-checklist-n`}>
                                                                                                                                    <td>
                                                                                                                                        {Object.entries(
                                                                                                                                            event?.eventType_RestrictedVisibility,
                                                                                                                                        )
                                                                                                                                            .filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    false,
                                                                                                                                            )
                                                                                                                                            .map(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) => {
                                                                                                                                                    return key
                                                                                                                                                        .replace(
                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                            ' $1',
                                                                                                                                                        )
                                                                                                                                                        .replace(
                                                                                                                                                            /^./,
                                                                                                                                                            (
                                                                                                                                                                str,
                                                                                                                                                            ) =>
                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                        )
                                                                                                                                                },
                                                                                                                                            )
                                                                                                                                            .join(
                                                                                                                                                ', ',
                                                                                                                                            )}
                                                                                                                                    </td>
                                                                                                                                    <td>
                                                                                                                                        Not
                                                                                                                                        Ok
                                                                                                                                    </td>
                                                                                                                                </tr>
                                                                                                                            )}
                                                                                                                        </tbody>
                                                                                                                    </table>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'CrewTraining' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Training
                                                                                </th>
                                                                                <td
                                                                                    colSpan={
                                                                                        4
                                                                                    }>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.crewTraining.id}-ct-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Event
                                                                                                        type:
                                                                                                    </strong>{' '}
                                                                                                    Crew
                                                                                                    training
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Training
                                                                                                        date:
                                                                                                    </strong>{' '}
                                                                                                    {formatDate(
                                                                                                        event
                                                                                                            .crewTraining
                                                                                                            .date,
                                                                                                    ) ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.crewTraining.id}-locationbc`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Start
                                                                                                        time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.crewTraining
                                                                                                        ?.startTime ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        End
                                                                                                        time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.crewTraining
                                                                                                        ?.finishTime ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.crewTraining.id}-trainer`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Trainer:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.crewTraining
                                                                                                        ?.trainer
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.crewTraining
                                                                                                              ?.trainer
                                                                                                              ?.firstName +
                                                                                                          ' ' +
                                                                                                          event
                                                                                                              ?.crewTraining
                                                                                                              ?.trainer
                                                                                                              ?.surname
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.crewTraining
                                                                                                        ?.geoLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.crewTraining
                                                                                                              ?.geoLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.crewTraining.id}-crew-trained`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Crew
                                                                                                        trained:
                                                                                                    </strong>{' '}
                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                        {event?.crewTraining?.members?.nodes?.map(
                                                                                                            (
                                                                                                                member: any,
                                                                                                                index: number,
                                                                                                            ) => (
                                                                                                                <React.Fragment
                                                                                                                    key={
                                                                                                                        member.id
                                                                                                                    }>
                                                                                                                    {index >
                                                                                                                        0 &&
                                                                                                                        ', '}
                                                                                                                    {`${member.firstName} ${member.surname}`}
                                                                                                                </React.Fragment>
                                                                                                            ),
                                                                                                        ) ||
                                                                                                            'N/A'}
                                                                                                    </div>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Training
                                                                                                        type:
                                                                                                    </strong>{' '}
                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                        {event?.crewTraining?.trainingTypes?.nodes?.map(
                                                                                                            (
                                                                                                                type: any,
                                                                                                                index: number,
                                                                                                            ) => (
                                                                                                                <React.Fragment
                                                                                                                    key={
                                                                                                                        type.id
                                                                                                                    }>
                                                                                                                    {index >
                                                                                                                        0 &&
                                                                                                                        ', '}
                                                                                                                    {`${type.title}`}
                                                                                                                </React.Fragment>
                                                                                                            ),
                                                                                                        ) ||
                                                                                                            'N/A'}
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                            {event
                                                                                                ?.crewTraining
                                                                                                ?.trainingSummary && (
                                                                                                <tr>
                                                                                                    <td
                                                                                                        colSpan={
                                                                                                            4
                                                                                                        }>
                                                                                                        <div className="flex">
                                                                                                            <strong>
                                                                                                                Training
                                                                                                                summary:
                                                                                                            </strong>{' '}
                                                                                                            <div className="inline-block">
                                                                                                                <div
                                                                                                                    dangerouslySetInnerHTML={{
                                                                                                                        __html: event
                                                                                                                            ?.crewTraining
                                                                                                                            ?.trainingSummary,
                                                                                                                    }}></div>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            )}
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'Tasking' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Tasking
                                                                                </th>
                                                                                <td
                                                                                    colSpan={
                                                                                        4
                                                                                    }>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.eventType_Tasking.id}-et-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Event
                                                                                                        type:
                                                                                                    </strong>{' '}
                                                                                                    {event?.eventType_Tasking?.type?.replace(
                                                                                                        /([A-Z])/g,
                                                                                                        ' $1',
                                                                                                    ) ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_Tasking
                                                                                                        ?.time ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.eventType_Tasking.id}-et2-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Title:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_Tasking
                                                                                                        ?.title ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_Tasking
                                                                                                        ?.geoLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.eventType_Tasking
                                                                                                              ?.geoLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.eventType_Tasking.id}-et3-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Activity
                                                                                                        type:
                                                                                                    </strong>{' '}
                                                                                                    {getActivityType(
                                                                                                        event.eventType_Tasking,
                                                                                                        trip,
                                                                                                    )}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Fuel:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_Tasking
                                                                                                        ?.fuelLog
                                                                                                        ?.nodes
                                                                                                        ?.length
                                                                                                        ? event
                                                                                                              ?.eventType_Tasking
                                                                                                              ?.fuelLog
                                                                                                              ?.nodes[0]
                                                                                                              ?.fuelAfter
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            {event
                                                                                                ?.eventType_Tasking
                                                                                                ?.vesselRescueID >
                                                                                                0 &&
                                                                                                event
                                                                                                    ?.eventType_Tasking
                                                                                                    ?.type ===
                                                                                                    'TaskingStartUnderway' && (
                                                                                                    <>
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr1-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Target
                                                                                                                    vessel:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.vesselName ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Call
                                                                                                                    sign:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.callSign ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr2-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    P.O.B:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.pob ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Location:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.vesselLocation
                                                                                                                    ?.id >
                                                                                                                0
                                                                                                                    ? event
                                                                                                                          ?.eventType_Tasking
                                                                                                                          ?.vesselRescue
                                                                                                                          ?.vesselLocation
                                                                                                                          ?.title
                                                                                                                    : 'N/A'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.vesselRescue
                                                                                                            ?.locationDescription && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-vr3-type`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <strong>
                                                                                                                        Location
                                                                                                                        description:
                                                                                                                    </strong>{' '}
                                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.locationDescription ||
                                                                                                                            'N/A'}
                                                                                                                    </div>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr4-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Vessel
                                                                                                                    length:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.vesselLength ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Vessel
                                                                                                                    type:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.vesselType ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.vesselRescue
                                                                                                            ?.vesselTypeDescription && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-vr5-type`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <strong>
                                                                                                                        Vessel
                                                                                                                        description:
                                                                                                                    </strong>{' '}
                                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.vesselTypeDescription ||
                                                                                                                            'N/A'}
                                                                                                                    </div>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr6-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Make
                                                                                                                    and
                                                                                                                    model:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.makeAndModel ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Vessel
                                                                                                                    color:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.color ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr7-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Owner
                                                                                                                    name:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.ownerName ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Owner
                                                                                                                    on
                                                                                                                    board?:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.ownerOnBoard
                                                                                                                    ? 'Yes'
                                                                                                                    : 'No'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr8-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Owner
                                                                                                                    phone:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.phone ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    CG
                                                                                                                    Membership:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.cgMembership ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr
                                                                                                            key={`${event.eventType_Tasking.id}-et-vr9-type`}>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Owner
                                                                                                                    email:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.email ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <strong>
                                                                                                                    Owner
                                                                                                                    address:
                                                                                                                </strong>{' '}
                                                                                                                {event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.vesselRescue
                                                                                                                    ?.address ||
                                                                                                                    'N/A'}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.cgop && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-incident-cgop`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <strong>
                                                                                                                        Incident
                                                                                                                        details:
                                                                                                                    </strong>{' '}
                                                                                                                    CoastGuard
                                                                                                                    Rescue
                                                                                                                    -{' '}
                                                                                                                    {event
                                                                                                                        ?.eventType_Tasking
                                                                                                                        ?.cgop ||
                                                                                                                        'N/A'}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.sarop && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-incident-sarop`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <strong>
                                                                                                                        Incident
                                                                                                                        details:
                                                                                                                    </strong>{' '}
                                                                                                                    SAROP
                                                                                                                    -{' '}
                                                                                                                    {event
                                                                                                                        ?.eventType_Tasking
                                                                                                                        ?.sarop ||
                                                                                                                        'N/A'}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                    </>
                                                                                                )}
                                                                                            {event
                                                                                                ?.eventType_Tasking
                                                                                                ?.vesselRescueID >
                                                                                                0 &&
                                                                                                event
                                                                                                    ?.eventType_Tasking
                                                                                                    ?.type ===
                                                                                                    'TaskingComplete' && (
                                                                                                    <>
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.vesselRescue
                                                                                                            ?.mission
                                                                                                            ?.id >
                                                                                                            0 && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-mission`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Mission outcome`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_Tasking?.vesselRescue?.mission?.operationOutcome?.replace(/_/g, ' ')}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.vesselRescue
                                                                                                            ?.missionTimeline
                                                                                                            ?.nodes
                                                                                                            ?.length >
                                                                                                            0 && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-mission-timeline`}>
                                                                                                                <td
                                                                                                                    className="px-6"
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <div className="pb-4">
                                                                                                                        {`Mission notes/comments`}
                                                                                                                    </div>
                                                                                                                    <table
                                                                                                                        className={
                                                                                                                            tableClass
                                                                                                                        }>
                                                                                                                        <tbody>
                                                                                                                            {event?.eventType_Tasking?.vesselRescue?.missionTimeline?.nodes?.map(
                                                                                                                                (
                                                                                                                                    mission: any,
                                                                                                                                ) => (
                                                                                                                                    <tr
                                                                                                                                        key={`${mission.id}-et-mission-timeline`}>
                                                                                                                                        <td>
                                                                                                                                            {`${formatDateTime(mission?.time)} - ${mission?.commentType}`}
                                                                                                                                        </td>
                                                                                                                                        <td>
                                                                                                                                            <div
                                                                                                                                                dangerouslySetInnerHTML={{
                                                                                                                                                    __html: mission?.description,
                                                                                                                                                }}></div>
                                                                                                                                        </td>
                                                                                                                                        <td>
                                                                                                                                            {`${mission?.author?.id > 0 ? mission?.author?.firstName + ' ' + mission?.author?.surname : ''}`}
                                                                                                                                        </td>
                                                                                                                                    </tr>
                                                                                                                                ),
                                                                                                                            )}
                                                                                                                        </tbody>
                                                                                                                    </table>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                    </>
                                                                                                )}
                                                                                            {event
                                                                                                ?.eventType_Tasking
                                                                                                ?.vesselRescueID >
                                                                                                0 &&
                                                                                                event
                                                                                                    ?.eventType_Tasking
                                                                                                    ?.type ===
                                                                                                    'TaskingOnTow' && (
                                                                                                    <>
                                                                                                        {event
                                                                                                            ?.eventType_Tasking
                                                                                                            ?.vesselRescue
                                                                                                            ?.mission
                                                                                                            ?.id >
                                                                                                            0 && (
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-mission`}>
                                                                                                                <td
                                                                                                                    className="px-6"
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <div className="pb-4">
                                                                                                                        {`Towing checklist - risk analysis`}
                                                                                                                    </div>
                                                                                                                    <table
                                                                                                                        className={
                                                                                                                            tableClass
                                                                                                                        }>
                                                                                                                        <tbody>
                                                                                                                            {Object.entries(
                                                                                                                                getTowingChecklist(
                                                                                                                                    event.eventType_Tasking,
                                                                                                                                    trip,
                                                                                                                                ),
                                                                                                                            ).filter(
                                                                                                                                ([
                                                                                                                                    key,
                                                                                                                                    value,
                                                                                                                                ]) =>
                                                                                                                                    value ===
                                                                                                                                    true,
                                                                                                                            )
                                                                                                                                .length >
                                                                                                                                0 && (
                                                                                                                                <tr>
                                                                                                                                    <td>
                                                                                                                                        {Object.entries(
                                                                                                                                            getTowingChecklist(
                                                                                                                                                event.eventType_Tasking,
                                                                                                                                                trip,
                                                                                                                                            ),
                                                                                                                                        )
                                                                                                                                            .filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    true,
                                                                                                                                            )
                                                                                                                                            .map(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) => ({
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                }),
                                                                                                                                            )
                                                                                                                                            .map(
                                                                                                                                                (
                                                                                                                                                    item,
                                                                                                                                                ) => {
                                                                                                                                                    return item.key
                                                                                                                                                        .replace(
                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                            ' $1',
                                                                                                                                                        )
                                                                                                                                                        .replace(
                                                                                                                                                            /^./,
                                                                                                                                                            (
                                                                                                                                                                str,
                                                                                                                                                            ) =>
                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                        )
                                                                                                                                                },
                                                                                                                                            )
                                                                                                                                            .join(
                                                                                                                                                ', ',
                                                                                                                                            )}
                                                                                                                                    </td>
                                                                                                                                    <td>
                                                                                                                                        Ok
                                                                                                                                    </td>
                                                                                                                                </tr>
                                                                                                                            )}
                                                                                                                            {Object.entries(
                                                                                                                                getTowingChecklist(
                                                                                                                                    event.eventType_Tasking,
                                                                                                                                    trip,
                                                                                                                                ),
                                                                                                                            ).filter(
                                                                                                                                ([
                                                                                                                                    key,
                                                                                                                                    value,
                                                                                                                                ]) =>
                                                                                                                                    value ===
                                                                                                                                    false,
                                                                                                                            )
                                                                                                                                .length >
                                                                                                                                0 && (
                                                                                                                                <tr>
                                                                                                                                    <td>
                                                                                                                                        {Object.entries(
                                                                                                                                            getTowingChecklist(
                                                                                                                                                event.eventType_Tasking,
                                                                                                                                                trip,
                                                                                                                                            ),
                                                                                                                                        )
                                                                                                                                            .filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    false,
                                                                                                                                            )
                                                                                                                                            .map(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) => ({
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                }),
                                                                                                                                            )
                                                                                                                                            .map(
                                                                                                                                                (
                                                                                                                                                    item,
                                                                                                                                                ) => {
                                                                                                                                                    return item.key
                                                                                                                                                        .replace(
                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                            ' $1',
                                                                                                                                                        )
                                                                                                                                                        .replace(
                                                                                                                                                            /^./,
                                                                                                                                                            (
                                                                                                                                                                str,
                                                                                                                                                            ) =>
                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                        )
                                                                                                                                                },
                                                                                                                                            )
                                                                                                                                            .join(
                                                                                                                                                ', ',
                                                                                                                                            )}
                                                                                                                                    </td>
                                                                                                                                    <td>
                                                                                                                                        Not
                                                                                                                                        Ok
                                                                                                                                    </td>
                                                                                                                                </tr>
                                                                                                                            )}
                                                                                                                            {getTowingChecklist(
                                                                                                                                event.eventType_Tasking,
                                                                                                                                trip,
                                                                                                                            )
                                                                                                                                ?.riskFactors
                                                                                                                                ?.nodes
                                                                                                                                ?.length >
                                                                                                                                0 && (
                                                                                                                                <tr>
                                                                                                                                    <td
                                                                                                                                        colSpan={
                                                                                                                                            2
                                                                                                                                        }>
                                                                                                                                        {getTowingChecklist(
                                                                                                                                            event.eventType_Tasking,
                                                                                                                                            trip,
                                                                                                                                        )?.riskFactors?.nodes?.map(
                                                                                                                                            (
                                                                                                                                                item: any,
                                                                                                                                                index: number,
                                                                                                                                            ) => (
                                                                                                                                                <div
                                                                                                                                                    className="inline-block"
                                                                                                                                                    key={
                                                                                                                                                        item.id
                                                                                                                                                    }>
                                                                                                                                                    {index >
                                                                                                                                                    0
                                                                                                                                                        ? ', '
                                                                                                                                                        : 'Risk factors: '}
                                                                                                                                                    {`${item.title} - ${item.impact} - ${item.probability}/10`}
                                                                                                                                                </div>
                                                                                                                                            ),
                                                                                                                                        )}
                                                                                                                                    </td>
                                                                                                                                </tr>
                                                                                                                            )}
                                                                                                                            {getTowingChecklist(
                                                                                                                                event.eventType_Tasking,
                                                                                                                                trip,
                                                                                                                            )
                                                                                                                                ?.member
                                                                                                                                ?.id >
                                                                                                                                0 && (
                                                                                                                                <tr>
                                                                                                                                    <td
                                                                                                                                        colSpan={
                                                                                                                                            2
                                                                                                                                        }>
                                                                                                                                        {`Author: ${getTowingChecklist(event.eventType_Tasking, trip)?.member?.firstName} ${getTowingChecklist(event.eventType_Tasking, trip)?.member?.surname}`}
                                                                                                                                    </td>
                                                                                                                                </tr>
                                                                                                                            )}
                                                                                                                        </tbody>
                                                                                                                    </table>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                    </>
                                                                                                )}
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'RefuellingBunkering' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Refueling
                                                                                    and
                                                                                    Bunkering
                                                                                </th>
                                                                                <td>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.eventType_RefuellingBunkering.id}-rb-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Date:
                                                                                                    </strong>{' '}
                                                                                                    {formatDateTime(
                                                                                                        event
                                                                                                            ?.eventType_RefuellingBunkering
                                                                                                            ?.date,
                                                                                                    ) ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RefuellingBunkering
                                                                                                        ?.geoLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.eventType_RefuellingBunkering
                                                                                                              ?.geoLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-fuel-details`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Fuel
                                                                                                        added:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RefuellingBunkering
                                                                                                        ?.fuelLog
                                                                                                        ?.nodes
                                                                                                        ?.length
                                                                                                        ? event
                                                                                                              ?.eventType_RefuellingBunkering
                                                                                                              ?.fuelLog
                                                                                                              ?.nodes[0]
                                                                                                              ?.fuelAdded
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Fuel
                                                                                                        level:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_RefuellingBunkering
                                                                                                        ?.fuelLog
                                                                                                        ?.nodes
                                                                                                        ?.length
                                                                                                        ? event
                                                                                                              ?.eventType_RefuellingBunkering
                                                                                                              ?.fuelLog
                                                                                                              ?.nodes[0]
                                                                                                              ?.fuelAfter
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            {event
                                                                                                ?.eventType_RefuellingBunkering
                                                                                                ?.notes && (
                                                                                                <tr>
                                                                                                    <td
                                                                                                        colSpan={
                                                                                                            2
                                                                                                        }>
                                                                                                        <div className="flex">
                                                                                                            <span className="min-w-48 inline-block">
                                                                                                                Notes
                                                                                                            </span>
                                                                                                            <div className="inline-block">
                                                                                                                <div
                                                                                                                    dangerouslySetInnerHTML={{
                                                                                                                        __html: event
                                                                                                                            ?.eventType_RefuellingBunkering
                                                                                                                            ?.notes,
                                                                                                                    }}></div>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            )}
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'PassengerDropFacility' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -{' '}
                                                                                    {event?.eventType_PassengerDropFacility?.type?.replace(
                                                                                        'Passenger',
                                                                                        '',
                                                                                    ) ||
                                                                                        'Drop Facility'}
                                                                                </th>
                                                                                <td>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.eventType_PassengerDropFacility.id}-pdf-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Title:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_PassengerDropFacility
                                                                                                        ?.title ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_PassengerDropFacility
                                                                                                        ?.time ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-location-fuel`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Location:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_PassengerDropFacility
                                                                                                        ?.geoLocation
                                                                                                        ?.id >
                                                                                                    0
                                                                                                        ? event
                                                                                                              ?.eventType_PassengerDropFacility
                                                                                                              ?.geoLocation
                                                                                                              ?.title
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Fuel:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.eventType_PassengerDropFacility
                                                                                                        ?.fuelLog
                                                                                                        ?.nodes
                                                                                                        ?.length
                                                                                                        ? event
                                                                                                              ?.eventType_PassengerDropFacility
                                                                                                              ?.fuelLog
                                                                                                              ?.nodes[0]
                                                                                                              ?.fuelAfter
                                                                                                        : 'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'EventSupernumerary' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Supernumerary
                                                                                </th>
                                                                                <td>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        <tbody>
                                                                                            <tr
                                                                                                key={`${event.supernumerary.id}-sup-type`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Title:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.supernumerary
                                                                                                        ?.title ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Briefing
                                                                                                        time:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.supernumerary
                                                                                                        ?.briefingTime ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr
                                                                                                key={`${event.id}-sup-time`}>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Total
                                                                                                        guests:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.supernumerary
                                                                                                        ?.totalGuest ||
                                                                                                        'N/A'}
                                                                                                </td>
                                                                                                <td>
                                                                                                    <strong>
                                                                                                        Is
                                                                                                        briefed?:
                                                                                                    </strong>{' '}
                                                                                                    {event
                                                                                                        ?.supernumerary
                                                                                                        ?.isBriefed
                                                                                                        ? 'Yes'
                                                                                                        : 'No'}
                                                                                                </td>
                                                                                            </tr>
                                                                                            {event
                                                                                                ?.supernumerary
                                                                                                ?.guestList
                                                                                                ?.nodes
                                                                                                ?.length >
                                                                                                0 && (
                                                                                                <tr
                                                                                                    key={`${event.id}-guest-list`}>
                                                                                                    <td
                                                                                                        colSpan={
                                                                                                            2
                                                                                                        }>
                                                                                                        <strong>
                                                                                                            Guests
                                                                                                            list:
                                                                                                        </strong>{' '}
                                                                                                        {event?.supernumerary?.guestList?.nodes?.map(
                                                                                                            (
                                                                                                                guest: any,
                                                                                                            ) => (
                                                                                                                <div
                                                                                                                    key={
                                                                                                                        guest.id
                                                                                                                    }>
                                                                                                                    {`${guest.firstName} ${guest.surname}`}
                                                                                                                </div>
                                                                                                            ),
                                                                                                        )}
                                                                                                    </td>
                                                                                                </tr>
                                                                                            )}
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                    {event.eventCategory ===
                                                                        'InfringementNotice' && (
                                                                        <>
                                                                            <tr>
                                                                                <th>
                                                                                    Activity
                                                                                    -
                                                                                    Infringement
                                                                                    Notice
                                                                                </th>
                                                                                <td>
                                                                                    <table
                                                                                        className={cn(
                                                                                            tableClass,
                                                                                            '[&_td:first-child]:!px-0',
                                                                                        )}>
                                                                                        {getInfringementsBody(
                                                                                            event.infringementNoticeID,
                                                                                        )}
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </>
                                                                    )}
                                                                </>
                                                            ),
                                                        )}
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </>
                                )}
                                <tr>
                                    <th>Arrival</th>
                                    <td>
                                        {`Expected time: ${trip?.arriveTime} - Actual time: ${dayjs(trip?.arrive).format('HH:mm:ss')}`}
                                    </td>
                                    <th>Arrival location</th>
                                    <td>
                                        {trip?.toLocation?.id > 0
                                            ? trip?.toLocation?.title
                                            : trip?.toLocation?.lat +
                                              ' ' +
                                              trip?.toLocation?.long}
                                    </td>
                                </tr>
                                {trip?.comment && (
                                    <tr>
                                        <th>Comment</th>
                                        <td colSpan={2}>
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: trip?.comment,
                                                }}></div>
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                ))}
        </div>
    )
}
