#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Music/SeaLogsV2/sealogs-frontend/node_modules/.pnpm/electron-builder@24.13.3_el_2d94b8d0e843602ee4381a4d0eb844a7/node_modules/electron-builder/node_modules:/mnt/c/Users/<USER>/Music/SeaLogsV2/sealogs-frontend/node_modules/.pnpm/electron-builder@24.13.3_el_2d94b8d0e843602ee4381a4d0eb844a7/node_modules:/mnt/c/Users/<USER>/Music/SeaLogsV2/sealogs-frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Music/SeaLogsV2/sealogs-frontend/node_modules/.pnpm/electron-builder@24.13.3_el_2d94b8d0e843602ee4381a4d0eb844a7/node_modules/electron-builder/node_modules:/mnt/c/Users/<USER>/Music/SeaLogsV2/sealogs-frontend/node_modules/.pnpm/electron-builder@24.13.3_el_2d94b8d0e843602ee4381a4d0eb844a7/node_modules:/mnt/c/Users/<USER>/Music/SeaLogsV2/sealogs-frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../electron-builder/cli.js" "$@"
else
  exec node  "$basedir/../electron-builder/cli.js" "$@"
fi
