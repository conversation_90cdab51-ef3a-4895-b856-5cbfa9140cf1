@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules\@capacitor\assets\bin\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules\@capacitor\assets\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules\@capacitor\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules\@capacitor\assets\bin\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules\@capacitor\assets\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules\@capacitor\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\@capacitor+assets@3.0.5_@types+node@20.19.2_typescript@5.8.3\node_modules;C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@capacitor\assets\bin\capacitor-assets" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@capacitor\assets\bin\capacitor-assets" %*
)
